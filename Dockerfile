# Stage 1: Dependencies
FROM node:22-alpine AS dependencies

WORKDIR /usr/src/app

RUN corepack enable

# Copy package files
COPY package.json yarn.lock ./
COPY apps/api/package.json ./apps/api/
COPY apps/web/package.json ./apps/web/

# Install all dependencies (including dev dependencies for building)
RUN yarn install

# Stage 2: Build frontend
FROM dependencies AS frontend-builder

# Copy frontend source code
COPY apps/web ./apps/web

# Build frontend
RUN cd apps/web && yarn build

# Stage 3: Build API
FROM dependencies AS api-builder

# Copy API source code
COPY apps/api ./apps/api

# Build API
RUN cd apps/api && yarn build

# Stage 4: Production dependencies
FROM node:22-alpine AS prod-dependencies

WORKDIR /usr/src/app

RUN corepack enable

# Copy package files
COPY package.json yarn.lock ./
COPY apps/api/package.json ./apps/api/

# Install only production dependencies
RUN yarn workspaces focus api --production && yarn cache clean

# Stage 5: Final production image
FROM node:22-alpine

WORKDIR /usr/src/app

# Copy production dependencies
COPY --from=prod-dependencies /usr/src/app/node_modules ./node_modules
COPY --from=prod-dependencies /usr/src/app/apps/api/node_modules ./apps/api/node_modules

# Copy built API
COPY --from=api-builder /usr/src/app/apps/api/dist ./
COPY --from=api-builder /usr/src/app/apps/api/package.json ./package.json

# Copy built frontend to the frontend directory
COPY --from=frontend-builder /usr/src/app/apps/web/dist ./frontend

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Change ownership of the app directory
RUN chown -R nodejs:nodejs /usr/src/app
USER nodejs

EXPOSE 3000

CMD ["node", "index.js"]
