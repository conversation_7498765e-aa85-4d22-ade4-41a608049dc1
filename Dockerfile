FROM node:22-alpine AS builder

WORKDIR /usr/src/app

RUN corepack enable
RUN mkdir -p apps/api
RUN mkdir -p apps/web

COPY package.json yarn.lock ./
COPY apps/api/package.json apps/api/yarn.lock
COPY apps/web/package.json apps/web/yarn.lock

RUN yarn install

COPY . .

RUN yarn build

FROM node:22-alpine

WORKDIR /usr/src/app

COPY --from=builder /usr/src/app/dist /usr/src/app/dist

CMD ["node", "dist/apps/api/main.js"]
